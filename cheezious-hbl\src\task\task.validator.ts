import type { ApiEvent } from '../../shared/api.interfaces';
import { ErrorCode } from '../../shared/error-codes';
import { BadRequestResult } from '../../shared/errors';

interface ValidatedIncomingTask {
  text: string;
}

export const validateIncomingTask = (
  event: ApiEvent,
): ValidatedIncomingTask => {
  const body = JSON.parse(event.body ?? '{}');

  let text: string | undefined;

  if (body.text !== undefined) {
    text = String(body.text).trim();
  }

  // if no fields are present
  if (!text) {
    throw new BadRequestResult(
      ErrorCode.MissingProperties,
      'Missing properties',
    );
  }

  return { text };
};
