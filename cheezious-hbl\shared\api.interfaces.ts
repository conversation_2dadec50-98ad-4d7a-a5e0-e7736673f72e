import type {
  APIGatewayEvent,
  Context,
  ProxyCallback,
  ProxyResult,
} from 'aws-lambda';
import type { JwtPayload } from 'jsonwebtoken';

import type { ErrorResult } from './errors';

// Type aliases to hide the 'aws-lambda' package and have consistent, short naming.
export type ApiCallback = ProxyCallback;
export type ApiContext = Context;
export type ApiEvent = APIGatewayEvent & {
  jwtData?: JwtPayload;
  jwsData?: {
    [key: string]: any;
  };
};
export type ApiHandler = (
  event: ApiEvent,
  context: Context,
  callback: ApiCallback,
) => void; // Same as ProxyHand<PERSON>, but requires callback.
export type ApiResponse = ProxyResult;

export interface ErrorResponseBody {
  error: ErrorResult;
}

export interface S3SignedUrlResult {
  data: {
    key: string;
    url: string;
  };
}

export interface GenericResponse {
  data: {
    message: string;
  };
}

export interface PaginationParams {
  count: number;
  offset: number;
  limit: number;
  order: 'ASC' | 'DESC';
  search: string | undefined;
}

export interface CustomJwtPayload {
  clientId: string | undefined;
}
