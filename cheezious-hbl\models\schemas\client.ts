import { DataTypes, Model } from 'sequelize';

class Client extends Model {
  declare id: number;

  declare name: string;

  declare email: string;

  declare webhook: string;
}

const schema = (sequelize: any) => {
  return Client.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
        allowNull: false,
        unique: true,
      },
      name: {
        type: new DataTypes.STRING(255),
        allowNull: false,
        unique: true,
      },
      email: {
        type: new DataTypes.STRING(255),
        allowNull: false,
        unique: true,
      },
      webhook: {
        type: new DataTypes.STRING(255),
        allowNull: true,
      },
    },
    {
      timestamps: true,
      sequelize, // passing the `sequelize` instance is required
      paranoid: true,
    },
  );
};

export default schema;
