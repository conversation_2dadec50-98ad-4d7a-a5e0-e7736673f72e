import { Op } from 'sequelize';

import { SequelizeConnectionBuilder } from '../../shared/sequelize-connection-builder';
import type { Task } from './task.interface';

export class TasksRepository {
  public async create(data: { text: string }, clientId: string): Promise<Task> {
    const { taskModel } = SequelizeConnectionBuilder.models;
    let response = null;

    try {
      response = await taskModel.create({ ...data, clientId });
    } catch (e) {
      console.log(e);
    }
    return response?.toJSON();
  }

  public async list(
    clientId: string,
    search: string | undefined,
    offset: number,
    limit: number,
    order: 'ASC' | 'DESC' = 'DESC',
    orderBy: string = 'createdAt',
  ): Promise<{ count: number; rows: Array<Task> }> {
    const { taskModel } = SequelizeConnectionBuilder.models;
    let response = null;
    const whereClause: {
      text?: {
        [Op.like]: string;
      };
      isActive?: boolean;
    } = {};

    if (search) {
      whereClause.text = {
        [Op.like]: `%${search ?? ''}%`,
      };
    }
    try {
      response = await taskModel.findAndCountAll({
        where: { ...whereClause, clientId },
        order: [[orderBy, order]],
        offset,
        limit,
        distinct: true,
      });
    } catch (e) {
      console.log(e);
    }
    return {
      count: response.count,
      rows: response.rows.map((obj: any) => obj.toJSON()),
    };
  }
}
