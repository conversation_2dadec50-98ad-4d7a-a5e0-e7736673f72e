import type { ApiEvent, PaginationParams } from './api.interfaces';

export const validatePaginationParams = (
  event: ApiEvent,
): Omit<PaginationParams, 'count'> => {
  let offset: number = 0;
  let limit: number = 10;
  let order: 'ASC' | 'DESC' = 'ASC';

  const queryParams = event.queryStringParameters ?? {};

  if (Number(queryParams.offset) >= 0) {
    offset = Number(queryParams.offset) ?? 0;
  }
  if (Number(queryParams.limit)) {
    limit = Number(queryParams.limit) ?? 10;
  }
  const tempOrder = queryParams.order?.toUpperCase();
  if (tempOrder === 'ASC' || tempOrder === 'DESC') {
    order = tempOrder;
  }

  return {
    offset,
    limit,
    order,
    search: queryParams.search,
  };
};
