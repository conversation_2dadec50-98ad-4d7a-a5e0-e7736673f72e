import { SequelizeConnectionBuilder } from '../../shared/sequelize-connection-builder';
import type { Client } from './clients.interface';

export class ClientsRepository {
  public async create(name: string, email: string): Promise<Client> {
    const { clientModel } = SequelizeConnectionBuilder.models;
    let response = null;

    try {
      response = await clientModel.create({
        name,
        email,
      });
    } catch (e) {
      console.log(e);
    }
    return response?.toJSON();
  }
}
