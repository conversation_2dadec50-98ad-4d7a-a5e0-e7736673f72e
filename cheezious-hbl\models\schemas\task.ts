import { DataTypes, Model } from 'sequelize';

class Task extends Model {
  declare id: number;

  declare text: string;

  declare clientId: string;
}

const schema = (sequelize: any) => {
  return Task.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
        allowNull: false,
        unique: true,
      },
      text: {
        type: new DataTypes.STRING(255),
        allowNull: false,
      },
      clientId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'Clients',
          key: 'id',
        },
      },
    },
    {
      timestamps: true,
      sequelize, // passing the `sequelize` instance is required
      paranoid: true,
    },
  );
};

export default schema;
