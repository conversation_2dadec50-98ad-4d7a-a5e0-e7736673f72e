import { Sequelize } from 'sequelize';

import config from '../config';
import clientSchema from '../models/schemas/client';
import taskSchema from '../models/schemas/task';

export class SequelizeConnectionBuilder {
  public static sequelize: Sequelize;

  public static models: any;

  public static async initSequelize(): Promise<Sequelize> {
    // re-use the sequelize instance across invocations to improve performance
    if (!this.sequelize) {
      const { database, username, password, options } = config.database;

      const sequelize = new Sequelize(database, username, password, options);

      const clientModel = clientSchema(sequelize);
      const taskModel = taskSchema(sequelize);

      const models = {
        clientModel,
        taskModel,
      };
      // client has many tasks
      clientModel.hasMany(taskModel, {
        foreignKey: 'clientId',
        as: 'tasks',
      });
      taskModel.belongsTo(clientModel, {
        foreignKey: 'clientId',
        as: 'client',
      });
      // relations are defined here

      try {
        await sequelize.authenticate();
        // await sequelize.sync({ alter: true });
      } catch (error) {
        console.error('Unable to connect to the database:', error);
      }

      this.sequelize = sequelize;
      this.models = models;
    } else {
      // restart connection pool to ensure connections are not re-used across invocations
      this.sequelize.connectionManager.initPools();

      // restore `getConnection()` if it has been overwritten by `close()`
      // eslint-disable-next-line no-prototype-builtins
      if (this.sequelize.connectionManager.hasOwnProperty('getConnection')) {
        // @ts-ignore
        delete this.sequelize.connectionManager.getConnection;
      }
    }

    return this.sequelize;
  }
}
