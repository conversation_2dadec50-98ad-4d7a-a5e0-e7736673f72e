import * as middy from "middy";
import { doNotWaitForEmptyEventLoop } from "middy/middlewares";

import { onErrorMiddleware } from "../../middlewares";
import { HblController } from "./hbl.controller";
import { HblService } from "./hbl.service";

const service: HblService = new HblService();
const controller: HblController = new HblController(service);

export const decode = middy(controller.decode)
  .use(
    doNotWaitForEmptyEventLoop({
      runOnError: true,
      runOnBefore: true,
      runOnAfter: true,
    })
  )
  .use(onErrorMiddleware());

export const create = middy(controller.create)
  .use(
    doNotWaitForEmptyEventLoop({
      runOnError: true,
      runOnBefore: true,
      runOnAfter: true,
    })
  )
  .use(onErrorMiddleware());

  export const refundToHBL = middy(controller.refund)
  .use(
    doNotWaitForEmptyEventLoop({
      runOnError: true,
      runOnBefore: true,
      runOnAfter: true,
    })
  )
  .use(onErrorMiddleware());
