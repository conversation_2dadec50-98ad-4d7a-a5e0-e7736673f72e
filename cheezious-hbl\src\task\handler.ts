import * as middy from 'middy';
import { doNotWaitForEmptyEventLoop } from 'middy/middlewares';

import {
  decodeJWSMiddleware,
  onErrorMiddleware,
  sequelizeMiddleware,
} from '../../middlewares';
import { TasksController } from './task.controller';
import { TasksRepository } from './task.repository';
import { TasksService } from './task.service';

const repository: TasksRepository = new TasksRepository();
const service: TasksService = new TasksService(repository);
const controller: TasksController = new TasksController(service);

export const list = middy(controller.list)
  .use(
    doNotWaitForEmptyEventLoop({
      runOnError: true,
      runOnBefore: true,
      runOnAfter: true,
    }),
  )
  .use(sequelizeMiddleware())
  .use(onErrorMiddleware())
  .use(decodeJWSMiddleware());

export const create = middy(controller.create)
  .use(
    doNotWaitForEmptyEventLoop({
      runOnError: true,
      runOnBefore: true,
      runOnAfter: true,
    }),
  )
  .use(sequelizeMiddleware())
  .use(onErrorMiddleware())
  .use(decodeJWSMiddleware());
