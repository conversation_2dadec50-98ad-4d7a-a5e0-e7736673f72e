import type { ApiResponse, ErrorResponseBody } from '../shared/api.interfaces';

export interface ApiResponseParsed<T> extends ApiResponse {
  parsedBody: T;
}
export interface ApiErrorResponseParsed extends ApiResponse {
  parsedBody: ErrorResponseBody;
}
export interface QueryStringParameters {
  [name: string]: string;
}
export interface PathParameters {
  [name: string]: string;
}
export interface Body {
  [name: string]: object | string | number | boolean | null;
}
export interface JwtData {
  [name: string]: string;
}
export interface Headers {
  [name: string]: string;
}
