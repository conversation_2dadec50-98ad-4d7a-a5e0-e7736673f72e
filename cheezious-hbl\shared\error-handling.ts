import type { ApiCallback } from './api.interfaces';
import { BadRequestResult, ForbiddenResult, NotFoundResult } from './errors';
import { ResponseBuilder } from './response-builder';

export const generateError = (error: any, callback: ApiCallback) => {
  if (error instanceof NotFoundResult) {
    return ResponseBuilder.notFound(error.code, error.description, callback);
  }
  if (error instanceof ForbiddenResult) {
    return ResponseBuilder.forbidden(error.code, error.description, callback);
  }
  if (error instanceof BadRequestResult) {
    return ResponseBuilder.badRequest(error.code, error.description, callback);
  }

  console.log(error);
  return ResponseBuilder.internalServerError(error, callback);
};
