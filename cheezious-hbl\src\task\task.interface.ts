import type { PaginationParams } from '../../shared/api.interfaces';

export interface Task {
  id: string;
  text: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
}

export class TaskResponseEntity {
  id: string;

  text: string;

  createdAt: Date;

  updatedAt: Date;

  constructor(subtype: Task, caller: string) {
    this.id = subtype.id;
    this.text = subtype.text;
    this.createdAt = subtype.createdAt;
    this.updatedAt = subtype.updatedAt;
  }
}

export interface CreateResult {
  data: {
    task: TaskResponseEntity;
  };
}
export interface ListResult {
  data: Omit<PaginationParams, 'search'> & {
    tasks: Array<TaskResponseEntity>;
  };
}
