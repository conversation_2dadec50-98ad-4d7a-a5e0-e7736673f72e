{"name": "crm-backend", "version": "1.0.0", "scripts": {"setup:husky": "husky install", "analyse": "npm run lint && npm run test", "clean": "rimraf .build .serverless .nyc_output coverage", "coverage": "nyc report --reporter=text-lcov | coveralls", "prebuild": "npm run clean && npm run analyse", "build": "serverless package", "deploy:dev": "NODE_ENV=dev serverless deploy --stage dev", "deploy:prod": "NODE_ENV=prod serverless deploy --stage prod", "deploy": "serverless deploy", "postdeploy": "npm run test:integration", "start": "NODE_ENV=offline nodemon --delay 2000ms -e ts --exec \"sls offline start --noAuth --stage local\"", "pretest": "npm run clean", "test": "nyc mocha --config ./test/.mocharc.yml --timeout 20000 -r dotenv/config --exit", "test:integration": "mocha --config ./test/integration/.mocharc.yml --timeout 10000", "lint": "eslint --fix ."}, "repository": {"type": "git", "url": "git://github.com/frizhub-repo/cheezious-backend.git"}, "dependencies": {"@aws-sdk/client-s3": "^3.450.0", "@aws-sdk/s3-request-presigner": "^3.450.0", "@middy/core": "^4.0.9", "@middy/http-event-normalizer": "^4.0.9", "aws-sdk": "^2.1540.0", "axios": "^1.6.5", "bcryptjs": "^2.4.3", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "chance": "^1.0.18", "crypto": "^1.0.1", "jsonwebtoken": "^9.0.0", "jws": "^4.0.0", "middy": "^0.36.0", "mocha": "^6.2.0", "moment": "^2.29.4", "mysql2": "^2.3.3", "request-promise-native": "^1.0.7", "sequelize": "^6.28.0", "serverless-dotenv-plugin": "^4.0.2", "serverless-offline": "^12.0.1", "serverless-plugin-typescript": "^2.1.4", "serverless-prune-plugin": "^2.0.1", "short-unique-id": "^5.0.3", "uuid": "^9.0.0"}, "devDependencies": {"@types/aws-lambda": "^8.10.109", "@types/bcryptjs": "^2.4.5", "@types/bluebird": "^3.5.38", "@types/chai": "^4.1.7", "@types/chai-as-promised": "^7.1.5", "@types/chance": "^1.0.4", "@types/jsonwebtoken": "^9.0.0", "@types/jws": "^3.2.9", "@types/mocha": "^5.2.7", "@types/node": "^18.11.18", "@types/request-promise-native": "^1.0.16", "@types/sequelize": "^4.28.14", "@types/uuid": "^9.0.0", "@types/validator": "^13.7.10", "eslint": "8.22.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^27.1.5", "eslint-plugin-jest-formatting": "^3.1.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-simple-import-sort": "^8.0.0", "eslint-plugin-unused-imports": "^2.0.0", "husky": "^8.0.3", "nodemon": "^2.0.20", "nyc": "^14.0.0", "rimraf": "^3.0.0", "serverless-plugin-common-excludes": "^4.0.0", "ts-mockito": "^2.4.2", "ts-node": "^8.3.0", "typescript": "^4.9.4"}}