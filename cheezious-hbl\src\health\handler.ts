import * as middy from 'middy';

import { onErrorMiddleware } from '../../middlewares';
import { HealthController } from './health.controller';

const controller: HealthController = new HealthController();

export const getHealthCheck = middy(controller.getHealthCheck).use(
  onErrorMiddleware(),
);
export const getHealthCheckDetailed = middy(
  controller.getHealthCheckDetailed,
).use(onErrorMiddleware());
