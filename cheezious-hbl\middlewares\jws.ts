import type { Signature } from 'jws';
import * as jws from 'jws';
import type * as middy from 'middy';

import type { ApiEvent, ApiResponse } from '../shared/api.interfaces';

export const decode = (): middy.MiddlewareObject<ApiEvent, ApiResponse> => {
  return {
    before: async (request): Promise<void> => {
      const { event } = request;
      const signature = event.headers['x-czcrm-key'] ?? '';
      try {
        const data: Signature = jws.decode(signature);
        event.jwsData = JSON.parse(data.payload ?? '{}');
      } catch (error: any) {
        console.log('failed-jws-decode: ', error.code, error.message);
      }
    },
  };
};
