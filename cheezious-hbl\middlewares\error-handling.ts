import type * as middy from 'middy';

import type { ApiEvent, ApiResponse } from '../shared/api.interfaces';
import {
  BadRequestResult,
  ForbiddenResult,
  NotFoundResult,
} from '../shared/errors';
import { ResponseBuilder } from '../shared/response-builder';

export const onError = (): middy.MiddlewareObject<ApiEvent, ApiResponse> => {
  return {
    onError: (request: any): Promise<void> => {
      return new Promise((resolve, _reject) => {
        const { error } = request;
        // TODO: uncomment before deploying
        console.log('[ERROR]', error.message);
        console.log('[ERROR TYPE]', error);

        if (error instanceof NotFoundResult) {
          ResponseBuilder.notFound(
            error.code,
            error.description,
            (_error, response) => {
              request.response = response;
              resolve();
            },
          );
        } else if (error instanceof ForbiddenResult) {
          ResponseBuilder.forbidden(
            error.code,
            error.description,
            (_error, response) => {
              request.response = response;
              resolve();
            },
          );
        } else if (error instanceof BadRequestResult) {
          ResponseBuilder.badRequest(
            error.code,
            error.description,
            (_error, response) => {
              request.response = response;
              resolve();
            },
          );
        } else {
          ResponseBuilder.internalServerError(
            { ...error, message: 'Something went wrong.' },
            (_error, response) => {
              request.response = response;
              resolve();
            },
          );
        }
      });
    },
  };
};
