import type { ApiEvent } from "../../shared/api.interfaces";
import { ErrorCode } from "../../shared/error-codes";
import { BadRequestResult } from "../../shared/errors";

interface ValidatedIncomingHblBody {
  subTotal: number;
  orderNo: string;
  customerId: string;
  customerfullName: string;
  customerPhoneNo: string;
  successUrl?: string;
  failureUrl?: string;
  city?: string;
  address: {
    location: string;
  };
  orderItems: [
    {
      productName: string;
      productQuantity: number;
      productBasePrice: number;
    },
  ];
}

interface ValidatedIncomingHblRefundBody {
  orderNo: string;
  amount: number;
 
}

interface HblParams {
  data: string;
}

export const validateIncomingHblBody = (
  event: ApiEvent
): ValidatedIncomingHblBody => {
  const body = JSON.parse(event.body ?? "{}");

  const {
    subTotal,
    orderNo,
    customerId,
    customerfullName,
    customerPhoneNo,
    address,
    orderItems,
    successUrl,
    failureUrl,
    city,
  } = body;

  if (!subTotal) {
    throw new BadRequestResult(
      ErrorCode.MissingProperties,
      "Missing properties subTotal"
    );
  }

  if (!orderNo) {
    throw new BadRequestResult(
      ErrorCode.MissingProperties,
      "Missing properties orderNo"
    );
  }

  if (!customerId) {
    throw new BadRequestResult(
      ErrorCode.MissingProperties,
      "Missing properties customerId"
    );
  }

  if (!customerfullName) {
    throw new BadRequestResult(
      ErrorCode.MissingProperties,
      "Missing properties customerfullName"
    );
  }

  if (!customerPhoneNo) {
    throw new BadRequestResult(
      ErrorCode.MissingProperties,
      "Missing properties customerPhoneNo"
    );
  }

  if (!address) {
    throw new BadRequestResult(
      ErrorCode.MissingProperties,
      "Missing properties address"
    );
  }

  if (!address.location) {
    throw new BadRequestResult(
      ErrorCode.MissingProperties,
      "Missing properties address location"
    );
  }

  if (!orderItems) {
    throw new BadRequestResult(
      ErrorCode.MissingProperties,
      "Missing properties orderItems"
    );
  }

  for (const item of orderItems) {
    if (!item.productName) {
      throw new BadRequestResult(
        ErrorCode.MissingProperties,
        "Missing properties orderItems productName"
      );
    }

    if (!item.productQuantity) {
      throw new BadRequestResult(
        ErrorCode.MissingProperties,
        "Missing properties orderItems productQuantity"
      );
    }

    if (!item.productBasePrice) {
      throw new BadRequestResult(
        ErrorCode.MissingProperties,
        "Missing properties orderItems productBasePrice"
      );
    }
  }

  return {
    subTotal,
    orderNo,
    customerId,
    customerfullName,
    customerPhoneNo,
    address,
    orderItems,
    successUrl,
    failureUrl,
    city,
  };
};

export const validateIncomingHblRefundBody = (
  event: ApiEvent
): ValidatedIncomingHblRefundBody => {
  const body = JSON.parse(event.body ?? "{}");

  const { orderNo, amount } = body;

  if (!orderNo) {
    throw new BadRequestResult(
      ErrorCode.MissingProperties,
      "Missing properties orderNo"
    );
  }

  

  if (!amount || typeof amount !== 'number' || amount <= 0) {
    throw new BadRequestResult(
      ErrorCode.MissingProperties,
      "Missing or invalid properties amount"
    );
  }

  return {
    orderNo,
    amount,
   
  };
};

export const validateHBLParams = (event: ApiEvent): HblParams => {
  const queryParams = event.queryStringParameters ?? {};

  if (queryParams.data === undefined) {
    throw new BadRequestResult(
      ErrorCode.MissingProperties,
      "Missing properties"
    );
  }

  return {
    data: queryParams.data,
  };
};
