// eslint-disable-next-line import/no-cycle

export interface Client {
  id: string;
  name: string;
  email: string;
  apiKeys?: Array<ApiKey>;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date | null;
}

export interface ApiKey {
  id: string;
  key: string;
  expiresAt?: number;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date | null;
}

export class ClientResponseEntity {
  id: string;

  name: string;

  email: string;

  apiKeys?: Array<ApiKey>;

  createdAt?: Date;

  updatedAt?: Date;

  constructor(client: Client, caller: string) {
    this.id = client.id;
    this.name = client.name;
    this.email = client.email;
    this.apiKeys = client.apiKeys;
    this.createdAt = client.createdAt;
    this.updatedAt = client.updatedAt;
  }
}
export interface GetClientResult {
  data: {
    client: ClientResponseEntity;
  };
}
export interface ListClientsResult {
  data: {
    clients: Array<ClientResponseEntity>;
  };
}

export interface CreateClientResult {
  data: {
    client: ClientResponseEntity;
  };
}

export interface DeleteClientResult {
  data: {
    message: string;
  };
}
