import type * as middy from 'middy';

import type { ApiEvent, ApiResponse } from '../shared/api.interfaces';
import { SequelizeConnectionBuilder } from '../shared/sequelize-connection-builder';

const sequelize = (): middy.MiddlewareObject<ApiEvent, ApiResponse> => {
  return {
    before: async (): Promise<void> => {
      await SequelizeConnectionBuilder.initSequelize();
    },
    after: async (): Promise<void> => {
      // close any opened connections during the invocation
      // this will wait for any in-progress queries to finish before closing the connections
      await SequelizeConnectionBuilder.sequelize.connectionManager.close();
    },
  };
};

export default sequelize;
