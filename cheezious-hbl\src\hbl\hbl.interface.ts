export interface HblBody {
  subTotal: number;
  orderNo: string;
  customerId: string;
  customerfullName: string;
  customerPhoneNo: string;
  successUrl?: string;
  failureUrl?: string;
  city?: string;
  address: {
    location: string;
  };
  orderItems: [
    {
      productName: string;
      productQuantity: number;
      productBasePrice: number;
    },
  ];
}

export interface HblRefundBody {
  orderNo: string;

  amount: number;
}


export interface EnvBody {
  HBL_PUBLIC_KEY: string;
  CHEEZIOUS_PRIIVATE_KEY: string;
}

export interface CreateUrl {
  url: string;
}
