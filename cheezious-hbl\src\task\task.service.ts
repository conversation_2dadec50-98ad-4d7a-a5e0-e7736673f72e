import type { Task } from './task.interface';
import type { TasksRepository } from './task.repository';

export class TasksService {
  public constructor(private readonly repo: TasksRepository) {}

  public async create(data: { text: string }, clientId: string): Promise<Task> {
    return this.repo.create(data, clientId);
  }

  public async list(
    clientId: string,
    search: string | undefined,
    offset: number,
    limit: number,
    order: 'ASC' | 'DESC',
  ): Promise<{ count: number; rows: Array<Task> }> {
    return this.repo.list(clientId, search, offset, limit, order);
  }
}
