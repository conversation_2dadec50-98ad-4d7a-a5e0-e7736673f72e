import type {
  <PERSON>pi<PERSON><PERSON><PERSON>,
  <PERSON>pi<PERSON>ontex<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>pi<PERSON><PERSON><PERSON>,
} from "../../shared/api.interfaces";
import { ResponseBuilder } from "../../shared/response-builder";
import { CreateUrl } from "./hbl.interface";
import type { HblService } from "./hbl.service";
import { validateHBLParams, validateIncomingHblBody, validateIncomingHblRefundBody } from "./hbl.validator";

export class HblController {
  public constructor(private readonly service: HblService) {}

  public create: ApiHandler = async (
    event: ApiEvent,
    context: ApiContext,
    callback: ApiCallback
  ): Promise<{ url: string } | void> => {
    const body = validateIncomingHblBody(event);

    return ResponseBuilder.ok<CreateUrl>(
      await this.service.createRequestToHBL(body),
      callback
    );
  };

  public decode: ApiHandler = async (
    event: ApiEvent,
    context: ApiContext,
    callback: ApiCallback
  ): Promise<void> => {
    const { data } = validateHBLParams(event);
    return ResponseBuilder.ok<string>(
      await this.service.Decrypt(data),
      callback
    );
  };

  public refund: ApiHandler = async (
    event: ApiEvent,
    context: ApiContext,
    callback: ApiCallback
  ): Promise<any> => {
    const body = validateIncomingHblRefundBody(event);

    // Construct HblRefundBody (clientGuid is generated in the service)
    const refundBody = {
      orderNo: body.orderNo,
      amount: body.amount
    };
    const res = await this.service.refundToHBL(refundBody)
    console.log("Res", res);
    //return res
    return ResponseBuilder.ok<string>(
      res,
      callback
    );
  };
}
