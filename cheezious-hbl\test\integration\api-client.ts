import * as rp from 'request-promise-native';

import config from '../../config';

export class ApiClient {
  private readonly options: rp.RequestPromiseOptions;

  public constructor() {
    this.options = {
      baseUrl: config.env.BASE_URI,
      method: 'GET',
      resolveWithFullResponse: true,
    };
  }

  public getHealthCheck(): rp.RequestPromise {
    return rp('/health/check', this.options);
  }
}
