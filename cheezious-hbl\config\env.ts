import type { Dialect } from 'sequelize';

export default {
  STAGE: process.env.STAGE! ?? 'dev',
  BASE_URI: process.env.BASE_URI! ?? 'unknown',
  AWS_BUCKET_KEY: process.env.AWS_BUCKET_KEY! ?? 'unknown',
  AWS_BUCKET_NAME: process.env.AWS_BUCKET_NAME! ?? 'unknown',
  AWS_S3_ACCESS_KEY_ID: process.env.AWS_S3_ACCESS_KEY_ID! ?? 'unknown',
  AWS_S3_REGION: process.env.AWS_S3_REGION! ?? 'unknown',
  AWS_S3_SECRET_ACCESS_KEY: process.env.AWS_S3_SECRET_ACCESS_KEY! ?? 'unknown',
  DB_DIALECT: (process.env.DB_DIALECT! ?? 'mysql') as Dialect,
  DB_NAME: process.env.DB_NAME! ?? 'db',
  DB_USERNAME: process.env.DB_USERNAME! ?? 'root',
  DB_PASSWORD: process.env.DB_PASSWORD! ?? '',
  DB_HOST: process.env.DB_HOST! ?? 'localhost',
  DB_PORT: process.env.DB_PORT! ?? '3306',
  ENV_FILE: process.env.ENV_FILE! ?? 'unknown',
  CLIENT_JWS_SECRET: process.env.CLIENT_JWS_SECRET! ?? 'unknown',
  LAMBDA_TIMEOUT: process.env.LAMBDA_TIMEOUT! ?? '30',
  HBL_USER_ID: process.env.HBL_USER_ID! ?? 'unknown',
  HBL_PASSWORD: process.env.HBL_PASSWORD! ?? 'unknown',
  HBL_CHANNEL: process.env.HBL_CHANNEL! ?? 'unknown',
  HBL_PUBLIC_KEY: process.env.HBL_PUBLIC_KEY! ?? 'unknown',
  HBL_URL: process.env.HBL_URL! ?? 'unknown',
  CHEEZIOUS_HBL_URL: process.env.CHEEZIOUS_HBL_URL! ?? 'unknown',
} as const;
