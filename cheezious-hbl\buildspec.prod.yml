version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 18
    commands:
      - npm ci --ignore-scripts
      - npm install -g serverless --silent
  pre_build:
    commands:
      - echo "STAGE=${STAGE}" >> .env
      - echo "AWS_BUCKET_KEY=${AWS_BUCKET_KEY}" >> .env
      - echo "AWS_BUCKET_NAME=${AWS_BUCKET_NAME}" >> .env
      - echo "AWS_S3_ACCESS_KEY_ID=${AWS_S3_ACCESS_KEY_ID}" >> .env
      - echo "AWS_S3_REGION=${AWS_S3_REGION}" >> .env
      - echo "AWS_S3_SECRET_ACCESS_KEY=${AWS_S3_SECRET_ACCESS_KEY}" >> .env
      - echo "DB_DIALECT=${DB_DIALECT}" >> .env
      - echo "DB_NAME=${DB_NAME}" >> .env
      - echo "DB_USERNAME=${DB_USERNAME}" >> .env
      - echo "DB_PASSWORD=${DB_PASSWORD}" >> .env
      - echo "DB_HOST=${DB_HOST}" >> .env
      - echo "DB_PORT=${DB_PORT}" >> .env
      - echo "ENV_FILE=${ENV_FILE}" >> .env
      - echo "CLIENT_JWS_SECRET=${CLIENT_JWS_SECRET}" >> .env
      - echo "LAMBDA_TIMEOUT=${LAMBDA_TIMEOUT}" >> .env
      - echo "HBL_USER_ID=${HBL_USER_ID}" >> .env
      - echo "HBL_PASSWORD=${HBL_PASSWORD}" >> .env
      - echo "HBL_CHANNEL=${HBL_CHANNEL}" >> .env
      - echo "HBL_PUBLIC_KEY=${HBL_PUBLIC_KEY}" >> .env
      - echo "HBL_URL=${HBL_URL}" >> .env
      - echo "CHEEZIOUS_HBL_URL=${CHEEZIOUS_HBL_URL}" >> .env
      - cat .env
  build:
    commands:
      - NODE_ENV=prod
      - npm run deploy -- --stage=prod
      - npm run test:integration