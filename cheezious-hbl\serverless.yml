service: cz-payment-gateways
frameworkVersion: "3"
configValidationMode: error

provider:
  name: aws
  runtime: nodejs18.x
  stage: ${self:custom.stage}
  region: ${self:custom.region}
  timeout: ${self:custom.timeout}
  iamRoleStatements:
    - Effect: Allow
      Action:
        - lambda:InvokeFunction
        - lambda:InvokeAsync
      Resource: "*"

custom:
  stage: ${opt:stage, "prod"}
  region: ${opt:region, "ap-southeast-1"}
  timeout: 30
  output:
    file: .serverless/output.json
  prune:
    automatic: true
    number: 3
  serverless-offline:
    host: "0.0.0.0"
    httpPort: 4001

resources:
  Resources:
    GatewayResponseDefault4XX:
      Type: "AWS::ApiGateway::GatewayResponse"
      Properties:
        ResponseParameters:
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'*'"
        ResponseType: DEFAULT_4XX
        RestApiId:
          Ref: "ApiGatewayRestApi"

functions:
  #  Keeping /health endpoints on top
  getHealthCheck:
    handler: src/health/handler.getHealthCheck
    events:
      - http:
          path: health/check
          method: get
          cors: true
  getHealthCheckDetailed:
    handler: src/health/handler.getHealthCheckDetailed
    events:
      - http:
          path: health/detailed
          method: get
          cors: true
  # Functional endpoints start from here
  # HBL routes
  hblDecode:
    handler: src/hbl/handler.decode
    events:
      - http:
          path: hbl
          method: get
          cors: true
  hblCreateUrl:
    handler: src/hbl/handler.create
    events:
      - http:
          path: hbl
          method: post
          cors: true
  hblRefund:
    handler: src/hbl/handler.refundToHBL
    events:
      - http:
          path: hbl-refund
          method: post
          cors: true

package:
  excludeDevDependencies: true

plugins:
  - serverless-plugin-typescript
  - serverless-dotenv-plugin
  - serverless-offline
  - serverless-prune-plugin
  - serverless-plugin-common-excludes
