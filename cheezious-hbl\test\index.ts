import * as middy from 'middy';
import { doNotWaitForEmptyEventLoop } from 'middy/middlewares';

import { decodeJWTMiddleware, onErrorMiddleware } from '../middlewares';
import type {
  ApiContext,
  ApiEvent,
  ApiHandler,
  ApiResponse,
  ErrorResponseBody,
} from '../shared/api.interfaces';
import type {
  ApiErrorResponseParsed,
  ApiResponseParsed,
  Body,
  Headers,
  PathParameters,
  QueryStringParameters,
} from './test.interfaces';

type SuccessCaller = <T>(
  handler: ApiHandler,
  data?: {
    queryStringParameters?: QueryStringParameters;
    pathParameters?: PathParameters;
    body?: Body;
    headers?: Headers;
  },
) => Promise<ApiResponseParsed<T>>;
type FailureCaller = (
  handler: ApiHandler,
  data?: {
    queryStringParameters?: QueryStringParameters;
    pathParameters?: PathParameters;
    body?: Body;
    headers?: Headers;
  },
) => Promise<ApiErrorResponseParsed>;

export const callSuccess: SuccessCaller = <T>(
  handler: <PERSON><PERSON><PERSON><PERSON><PERSON>,
  data?: {
    queryStringParameters?: QueryStringParameters;
    pathParameters?: PathParameters;
    body?: Body;
    headers?: Headers;
  },
): Promise<ApiResponseParsed<T>> => {
  return new Promise((resolve, reject) => {
    const event: ApiEvent = <ApiEvent>{};
    if (data?.queryStringParameters) {
      event.queryStringParameters = data?.queryStringParameters;
    }
    if (data?.pathParameters) {
      event.pathParameters = data?.pathParameters;
    }
    if (data?.body) {
      event.body = JSON.stringify(data?.body);
    }
    if (data?.headers) {
      event.headers = data?.headers;
    }
    handler(
      event,
      <ApiContext>{},
      (error?: Error | null | string, result?: ApiResponse): void => {
        if (typeof result === 'undefined') {
          reject(new Error('No result was returned by the handler!'));
          return;
        }

        const parsedResult: ApiResponseParsed<T> =
          result as ApiResponseParsed<T>;
        parsedResult.parsedBody = JSON.parse(result.body) as T;
        resolve(parsedResult);
      },
    );
  });
};

// tslint:disable-next-line arrow-return-shorthand (Long function body.)
export const callFailure: FailureCaller = (
  handler: ApiHandler,
  data?: {
    queryStringParameters?: QueryStringParameters;
    pathParameters?: PathParameters;
    body?: Body;
    headers?: Headers;
  },
): Promise<ApiErrorResponseParsed> => {
  // tslint:disable-next-line typedef (Well-known constructor.)
  return new Promise((resolve, reject) => {
    const event: ApiEvent = <ApiEvent>{};
    if (data?.queryStringParameters) {
      event.queryStringParameters = data?.queryStringParameters;
    }
    if (data?.pathParameters) {
      event.pathParameters = data?.pathParameters;
    }
    if (data?.body) {
      event.body = JSON.stringify(data?.body);
    }
    if (data?.headers) {
      event.headers = data?.headers;
    }

    handler(
      event,
      <ApiContext>{},
      (error?: Error | null | string, result?: ApiResponse): void => {
        if (typeof result === 'undefined') {
          reject(new Error('No result was returned by the handler!'));
          return;
        }

        const parsedResult: ApiErrorResponseParsed =
          result as ApiErrorResponseParsed;
        parsedResult.parsedBody = JSON.parse(result.body) as ErrorResponseBody;
        resolve(parsedResult);
      },
    );
  });
};
export const attachMiddlewares = (
  controllerFunction: ApiHandler,
  options: { auth: boolean } = { auth: false },
) => {
  let temp = middy(controllerFunction)
    .use(
      doNotWaitForEmptyEventLoop({
        runOnError: true,
        runOnBefore: true,
        runOnAfter: true,
      }),
    )
    .use(onErrorMiddleware());

  if (options.auth) {
    temp = temp.use(decodeJWTMiddleware());
  }

  return temp;
};
