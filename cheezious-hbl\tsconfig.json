{"compilerOptions": {"module": "commonjs", "target": "es2017", "lib": ["es2017"], "ignoreDeprecations": "5.0", "moduleResolution": "node", "outDir": ".build", "rootDir": "./", "sourceMap": true, "allowJs": true, "noImplicitAny": true, "noUnusedLocals": true, "noImplicitThis": true, "strictNullChecks": true, "noImplicitReturns": true, "preserveConstEnums": true, "suppressImplicitAnyIndexErrors": true, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "strict": true}, "exclude": ["node_modules", "coverage"]}