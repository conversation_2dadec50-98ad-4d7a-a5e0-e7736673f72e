import * as crypto from "crypto";
import axios from "axios";

import { ErrorCode } from "../../shared/error-codes";
import { InternalServerErrorResult } from "../../shared/errors";
import type { CreateUrl, EnvBody, HblBody,HblRefundBody } from "./hbl.interface";
import { v4 as uuidv4 } from 'uuid';


const AWS = require("aws-sdk");

AWS.config.update({
  accessKeyId: `${process.env.AWS_S3_ACCESS_KEY_ID}`,
  secretAccessKey: `${process.env.AWS_S3_SECRET_ACCESS_KEY}`,
  region: `${process.env.AWS_S3_REGION}`,
});

export class HblService {
  public async createRequestToHBL(body: HblBody): Promise<CreateUrl> {
    try {
      const envBody = await this.getS3Env();
      console.log(envBody);

      //   const random = crypto.randomUUID(); //Generating random order id
      const requestBody = {
        USER_ID: process.env.HBL_USER_ID, // Place Your Client ID
        PASSWORD: this.Encrypt(process.env.HBL_PASSWORD ?? "", envBody), // Place your password
        CLIENT_NAME: this.Encrypt(process.env.HBL_USER_ID ?? "", envBody), // Place Your Client ID
        RETURN_URL: this.Encrypt(
          body.successUrl ??
            "https://staging.cheezious.com/payment-success",
          envBody
        ), // Success respose redirect page
        CANCEL_URL: this.Encrypt(
          body.failureUrl ??
            "https://staging.cheezious.com/payment-failure",
          envBody
        ), // Cancel Respose redirect page
        CHANNEL: this.Encrypt(process.env.HBL_CHANNEL ?? "", envBody), // Place your Channel
        TYPE_ID: this.Encrypt("0", envBody), // Place your type id if provided
        ORDER: {
          DISCOUNT_ON_TOTAL: this.Encrypt("0", envBody), // Order Summary
          SUBTOTAL: this.Encrypt(`${body.subTotal}`, envBody),
          OrderSummaryDescription: await this.productItems(body, envBody),
        },
        SHIPPING_DETAIL: {
          NAME: this.Encrypt("DHL SERVICE", envBody),
          DELIEVERY_DAYS: this.Encrypt("7", envBody),
          SHIPPING_COST: this.Encrypt("0", envBody),
        },
        ADDITIONAL_DATA: {
          REFERENCE_NUMBER: this.Encrypt(body.orderNo, envBody),
          CUSTOMER_ID: this.Encrypt(`${body.customerId}`, envBody),
          CURRENCY: this.Encrypt("PKR", envBody),
          BILL_TO_FORENAME: this.Encrypt(body.customerfullName, envBody),
          BILL_TO_SURNAME: this.Encrypt(body.customerfullName, envBody),
          BILL_TO_EMAIL: this.Encrypt("<EMAIL>", envBody),
          BILL_TO_PHONE: this.Encrypt(`${body.customerPhoneNo}`, envBody),
          BILL_TO_ADDRESS_LINE: this.Encrypt(
            body?.address?.location
              ? body?.address?.location
              : "Lahore pakistan",
            envBody
          ),
          BILL_TO_ADDRESS_CITY: this.Encrypt(
            body?.city ? body?.city : "Lahore pakistan",
            envBody
          ),
          BILL_TO_ADDRESS_STATE: this.Encrypt("PB", envBody),
          BILL_TO_ADDRESS_COUNTRY: this.Encrypt("PK", envBody),
          BILL_TO_ADDRESS_POSTAL_CODE: this.Encrypt("-", envBody),
          SHIP_TO_FORENAME: this.Encrypt(body.customerfullName, envBody),
          SHIP_TO_SURNAME: this.Encrypt(body.customerfullName, envBody),
          SHIP_TO_PHONE: this.Encrypt(`${body.customerPhoneNo}`, envBody),
          SHIP_TO_ADDRESS_LINE: this.Encrypt(
            body?.address?.location
              ? body?.address?.location
              : "Lahore pakistan",
            envBody
          ),
          SHIP_TO_ADDRESS_CITY: this.Encrypt(
            body?.city ? body?.city : "Lahore pakistan",
            envBody
          ),
          SHIP_TO_ADDRESS_STATE: this.Encrypt("PB", envBody),
          SHIP_TO_ADDRESS_COUNTRY: this.Encrypt("PK", envBody),
          SHIP_TO_ADDRESS_POSTAL_CODE: this.Encrypt("-", envBody),
          MerchantFields: {
            MDD1: this.Encrypt("WC", envBody),
            MDD2: this.Encrypt("YES", envBody),
            MDD3: this.Encrypt("Cheezious", envBody),
            MDD5: this.Encrypt("NO", envBody),
            MDD6: this.Encrypt("Standard", envBody),
            MDD7: this.Encrypt("1", envBody),
            MDD8: this.Encrypt("Pakistan", envBody),
            MDD20: this.Encrypt("NO", envBody),
          },
        },
      };

      const response: any = await axios.post(
        `${process.env.HBL_URL}/api/checkout`,
        requestBody
      );
      console.log(response.data);
      return {
        url: `${
          process.env.HBL_URL
        }/site/index.html#/checkout?data=${Buffer.from(
          response.data.Data.SESSION_ID
        ).toString("base64")}`,
      };
    } catch (err) {
      console.log("HBL error : ", err);
      throw new InternalServerErrorResult(ErrorCode.Unknown, "HBL error");
    }
  }

  public async Decrypt(data: string): Promise<string> {
    try {
      const envBody = await this.getS3Env();
      data = data.replace(/ /g, "+");
      // TODO: replace spaces in data with + sign
      // TODO: data.replaceAll(" ", "+");
      // Paste Your Private Key here For Response decryption.
      const pvt = `-----BEGIN RSA PRIVATE KEY-----\n${envBody.CHEEZIOUS_PRIIVATE_KEY}\n-----END RSA PRIVATE KEY-----`;
      const DecryptedData = crypto.privateDecrypt(
        {
          // In order to decrypt the data, we need to specify the
          // same hashing function and padding scheme that we used to
          // this.encrypt the data in the previous step
          key: pvt,
          padding: crypto.constants.RSA_NO_PADDING,
        },

        // The decrypted data is of the Buffer type, which we can convert to a
        // string to reveal the original data
        Buffer.from(data, "base64")
      );
      // return DecryptedData;
      return DecryptedData.toString("utf8");
    } catch (err) {
      console.log("HBL error : ", err);
      throw new InternalServerErrorResult(ErrorCode.Unknown, "HBL error");
    }
  }

  public Encrypt(data: string, envBody: EnvBody): string {
    try {
      const pub = `-----BEGIN PUBLIC KEY-----\n${envBody.HBL_PUBLIC_KEY}\n-----END PUBLIC KEY-----`; // Place HBL Public Key for this.Encryption;
      const encryptedData = crypto.publicEncrypt(
        {
          key: pub,
          padding: crypto.constants.RSA_PKCS1_PADDING,
        },
        // We convert the data string to a buffer using `Buffer.from`
        // The this.encrypted data is in the form of bytes, so we print it in base64 format
        // so that it's displayed in a more readable form
        Buffer.from(data)
      );
      return encryptedData.toString("base64");
    } catch (error) {
      console.log("error : ", error);
      throw new InternalServerErrorResult(ErrorCode.Unknown, "HBL error");
    }
  }

  public async productItems(body: HblBody, envBody: EnvBody) {
    const OrderSummaryDescription = [];
    for (const item of body.orderItems) {
      OrderSummaryDescription.push({
        ITEM_NAME: this.Encrypt(`${item.productName}`, envBody), // Product information
        QUANTITY: this.Encrypt(
          `${item.productQuantity ? item.productQuantity : 1}`,
          envBody
        ),
        UNIT_PRICE: this.Encrypt(
          `${item.productBasePrice ? item.productBasePrice : 0}`,
          envBody
        ),
        CATEGORY: this.Encrypt("Cheezious", envBody),
        SUB_CATEGORY: this.Encrypt("Cheezious Sub", envBody),
      });
    }
    return OrderSummaryDescription;
  }

  public async getS3Env(): Promise<EnvBody> {
    const s3 = new AWS.S3();
    const params = {
      Bucket: `${process.env.AWS_BUCKET_NAME}`,
      Key: `${process.env.AWS_BUCKET_KEY}`,
    };
    const data = await s3.getObject(params).promise();
    return JSON.parse(data.Body.toString());
  }


public async refundToHBL(refundBody: HblRefundBody): Promise<any> {
      try {
        const envBody = await this.getS3Env(); 
        const generatedGuid = uuidv4();
        console.log("refundBody", refundBody)
        // check order id in transaction table and get transaction id from that
        // get refund amount and transaction id from transaction table

        const payload = {
            "Header": {
              "TranCode": "33",
              "Channel": this.Encrypt(process.env.HBL_CHANNEL ?? "", envBody),
              "UserID": process.env.HBL_USER_ID,
              "Password": this.Encrypt(process.env.HBL_PASSWORD ?? "", envBody),
              "ClientGuid": this.Encrypt(generatedGuid, envBody)
            },
            "Body":{
              "OrderReference": this.Encrypt(refundBody.orderNo, envBody),
              "Amount1": this.Encrypt(`${refundBody.amount}`, envBody),
              "Narration": this.Encrypt(process.env.HBL_CHANNEL ?? "",envBody),
            }
        };

        console.log("payload:", payload)
       
        
        const response = await axios.post(
        `https://testpaymentapi.hbl.com/OpenApiRest/api/OpenApi/ProcessTransaction`,
        payload
      );

      
      console.log(response);
      
      //if response is success then update transaction table with transaction id and transaction code 33

        return JSON.stringify(response)
      } catch (error) {

        console.log(error)
       //console.error("Refund error:", error instanceof Error ? error.message : String(error));
       throw new InternalServerErrorResult(ErrorCode.Unknown, "Refund failed");
      }
  }

}
