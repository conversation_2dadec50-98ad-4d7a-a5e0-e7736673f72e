import type { Signature } from 'jws';
import * as jws from 'jws';

import config from '../config';

const generatePolicy = (
  principalId: string,
  effect: string,
  resource: string,
  decodedToken: any,
) => {
  return {
    principalId,
    policyDocument: {
      Version: '2012-10-17',
      Statement: [
        {
          Action: 'execute-api:Invoke',
          Effect: effect,
          Resource: resource,
        },
      ],
    },
    context: decodedToken ?? {},
  };
};

export const authorizer = (event: any, context: any) => {
  const signature = event.authorizationToken;

  if (!signature) {
    console.error('[ No Signature set ]', event.authorizationToken, signature);
    context.succeed(generatePolicy('user', 'Deny', event.methodArn, {}));
    return;
  }

  try {
    console.log('CLIENT_JWS_SECRET', config.env.CLIENT_JWS_SECRET);
    const data: Signature = jws.decode(signature);

    if (!jws.verify(signature, 'HS256', config.env.CLIENT_JWS_SECRET)) {
      console.error('Signature verification failed');
      context.succeed(generatePolicy('user', 'Deny', event.methodArn, {}));
      return;
    }
    context.succeed(
      generatePolicy('user', 'Allow', '*', JSON.parse(data.payload ?? '{}')),
    );
  } catch (error: any) {
    console.error('[ ERROR clientOnlyAuthorizer ]: ', error.message, signature);
    throw Error('Unauthorized');
  }
};
