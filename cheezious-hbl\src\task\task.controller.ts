import type {
  <PERSON>pi<PERSON><PERSON><PERSON>,
  <PERSON>pi<PERSON>onte<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Api<PERSON>and<PERSON>,
} from '../../shared/api.interfaces';
import { ErrorCode } from '../../shared/error-codes';
import { ResponseBuilder } from '../../shared/response-builder';
import { validatePaginationParams } from '../../shared/validator';
import type { CreateResult, ListResult, Task } from './task.interface';
import { TaskResponseEntity } from './task.interface';
import type { TasksService } from './task.service';
import { validateIncomingTask } from './task.validator';

export class TasksController {
  public constructor(private readonly service: TasksService) {}

  public create: ApiHandler = async (
    event: ApiEvent,
    context: ApiContext,
    callback: ApiCallback,
  ): Promise<void> => {
    const clientId = event.jwsData?.clientId;

    const { text } = validateIncomingTask(event);

    // Input validations.
    if (!text) {
      return ResponseBuilder.badRequest(
        ErrorCode.MissingProperties,
        'Please provide a text',
        callback,
      );
    }

    const task: Task = await this.service.create(
      {
        text,
      },
      clientId,
    );
    if (!task) {
      throw new Error('Failed to create task');
    }

    return ResponseBuilder.ok<CreateResult>(
      {
        data: {
          task: new TaskResponseEntity(task, 'task'),
        },
      },
      callback,
    );
  };

  public list: ApiHandler = async (
    event: ApiEvent,
    context: ApiContext,
    callback: ApiCallback,
  ): Promise<void> => {
    const clientId = event.jwsData?.clientId;

    const { offset, limit, order, search } = validatePaginationParams(event);

    const { count, rows } = await this.service.list(
      clientId,
      search,
      offset,
      limit,
      order,
    );

    return ResponseBuilder.ok<ListResult>(
      {
        data: {
          count,
          offset,
          limit,
          order,
          tasks: rows.map((task: Task) => new TaskResponseEntity(task, 'task')),
        },
      },
      callback,
    );
  };
}
